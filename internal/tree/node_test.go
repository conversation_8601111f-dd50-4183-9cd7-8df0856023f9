package tree

import (
	"encoding/json"
	"fmt"
	"testing"

	"github.com/berrijam/mulberri/internal/data/features"
)

// ====================
// Feature Tests
// ====================

func TestNewFeature(t *testing.T) {
	tests := []struct {
		name        string
		featureName string
		featureType features.FeatureType
		shouldPanic bool
	}{
		{
			name:        "valid integer feature",
			featureName: "age",
			featureType: features.IntegerFeature,
			shouldPanic: false,
		},
		{
			name:        "valid float feature",
			featureName: "salary",
			featureType: features.FloatFeature,
			shouldPanic: false,
		},
		{
			name:        "valid string feature",
			featureName: "department",
			featureType: features.StringFeature,
			shouldPanic: false,
		},
		{
			name:        "empty feature name",
			featureName: "",
			featureType: features.IntegerFeature,
			shouldPanic: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.shouldPanic {
				defer func() {
					if r := recover(); r == nil {
						t.Error("expected panic but none occurred")
					}
				}()
			}

			feature := NewFeature(tt.featureName, tt.featureType)

			if !tt.shouldPanic {
				if feature.Name != tt.featureName {
					t.Errorf("expected name %s, got %s", tt.featureName, feature.Name)
				}
				if feature.Type != tt.featureType {
					t.Errorf("expected type %v, got %v", tt.featureType, feature.Type)
				}
			}
		})
	}
}

func TestFeature_IsNumerical(t *testing.T) {
	tests := []struct {
		name        string
		featureType features.FeatureType
		isNumerical bool
	}{
		{"integer feature", features.IntegerFeature, true},
		{"float feature", features.FloatFeature, true},
		{"string feature", features.StringFeature, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			feature := &Feature{Name: "test", Type: tt.featureType}
			if feature.IsNumerical() != tt.isNumerical {
				t.Errorf("expected IsNumerical() = %v, got %v", tt.isNumerical, feature.IsNumerical())
			}
		})
	}
}

func TestFeature_IsCategorical(t *testing.T) {
	tests := []struct {
		name          string
		featureType   features.FeatureType
		isCategorical bool
	}{
		{"integer feature", features.IntegerFeature, false},
		{"float feature", features.FloatFeature, false},
		{"string feature", features.StringFeature, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			feature := &Feature{Name: "test", Type: tt.featureType}
			if feature.IsCategorical() != tt.isCategorical {
				t.Errorf("expected IsCategorical() = %v, got %v", tt.isCategorical, feature.IsCategorical())
			}
		})
	}
}

// ====================
// LeafNode Tests
// ====================

func TestNewLeafNode(t *testing.T) {
	tests := []struct {
		name            string
		distribution    map[interface{}]int
		shouldPanic     bool
		expectedPred    interface{}
		expectedConf    float64
		expectedSamples int
	}{
		{
			name:            "valid single class",
			distribution:    map[interface{}]int{"approved": 10},
			shouldPanic:     false,
			expectedPred:    "approved",
			expectedConf:    1.0,
			expectedSamples: 10,
		},
		{
			name:            "valid multiple classes",
			distribution:    map[interface{}]int{"approved": 8, "rejected": 2},
			shouldPanic:     false,
			expectedPred:    "approved",
			expectedConf:    0.8,
			expectedSamples: 10,
		},
		{
			name:         "empty distribution",
			distribution: map[interface{}]int{},
			shouldPanic:  true,
		},
		{
			name:         "negative count",
			distribution: map[interface{}]int{"approved": -1},
			shouldPanic:  true,
		},
		{
			name:         "zero samples",
			distribution: map[interface{}]int{"approved": 0, "rejected": 0},
			shouldPanic:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.shouldPanic {
				defer func() {
					if r := recover(); r == nil {
						t.Error("expected panic but none occurred")
					}
				}()
			}

			leaf := NewLeafNode(tt.distribution)

			if !tt.shouldPanic {
				if leaf.Prediction != tt.expectedPred {
					t.Errorf("expected prediction %v, got %v", tt.expectedPred, leaf.Prediction)
				}
				if leaf.Confidence != tt.expectedConf {
					t.Errorf("expected confidence %.2f, got %.2f", tt.expectedConf, leaf.Confidence)
				}
				if leaf.Samples != tt.expectedSamples {
					t.Errorf("expected samples %d, got %d", tt.expectedSamples, leaf.Samples)
				}
			}
		})
	}
}

func TestLeafNode_Interface(t *testing.T) {
	distribution := map[interface{}]int{"yes": 7, "no": 3}
	leaf := NewLeafNode(distribution)

	// Test Node interface methods
	if !leaf.IsLeaf() {
		t.Error("leaf node should return true for IsLeaf()")
	}
	if leaf.GetSamples() != 10 {
		t.Errorf("expected 10 samples, got %d", leaf.GetSamples())
	}
	if leaf.GetMajorityClass() != "yes" {
		t.Errorf("expected majority class 'yes', got %v", leaf.GetMajorityClass())
	}
	if leaf.GetConfidence() != 0.7 {
		t.Errorf("expected confidence 0.7, got %.2f", leaf.GetConfidence())
	}
	if leaf.GetNodeType() != "leaf" {
		t.Errorf("expected node type 'leaf', got %s", leaf.GetNodeType())
	}

	dist := leaf.GetClassDistribution()
	if dist["yes"] != 7 || dist["no"] != 3 {
		t.Errorf("class distribution mismatch: %v", dist)
	}
}

func TestLeafNode_Validate(t *testing.T) {
	tests := []struct {
		name        string
		leaf        *LeafNode
		expectValid bool
	}{
		{
			name: "valid leaf",
			leaf: &LeafNode{
				Prediction:        "approved",
				ClassDistribution: map[interface{}]int{"approved": 5},
				Samples:           5,
				Confidence:        1.0,
			},
			expectValid: true,
		},
		{
			name: "nil prediction",
			leaf: &LeafNode{
				Prediction: nil,
				Samples:    5,
				Confidence: 1.0,
			},
			expectValid: false,
		},
		{
			name: "zero samples",
			leaf: &LeafNode{
				Prediction: "approved",
				Samples:    0,
				Confidence: 1.0,
			},
			expectValid: false,
		},
		{
			name: "invalid confidence high",
			leaf: &LeafNode{
				Prediction: "approved",
				Samples:    5,
				Confidence: 1.5,
			},
			expectValid: false,
		},
		{
			name: "invalid confidence low",
			leaf: &LeafNode{
				Prediction: "approved",
				Samples:    5,
				Confidence: -0.1,
			},
			expectValid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			isValid := tt.leaf.Validate()
			if tt.expectValid && !isValid {
				t.Error("expected valid node but validation failed")
			}
			if !tt.expectValid && isValid {
				t.Error("expected validation to fail but it passed")
			}
		})
	}
}

// ====================
// DecisionNode Tests
// ====================

func TestNewDecisionNode(t *testing.T) {
	feature := NewFeature("age", features.IntegerFeature)
	distribution := map[interface{}]int{"yes": 6, "no": 4}

	tests := []struct {
		name         string
		feature      *Feature
		splitValue   interface{}
		distribution map[interface{}]int
		shouldPanic  bool
	}{
		{
			name:         "valid decision node",
			feature:      feature,
			splitValue:   30.0,
			distribution: distribution,
			shouldPanic:  false,
		},
		{
			name:         "nil feature",
			feature:      nil,
			splitValue:   30.0,
			distribution: distribution,
			shouldPanic:  true,
		},
		{
			name:         "nil split value",
			feature:      feature,
			splitValue:   nil,
			distribution: distribution,
			shouldPanic:  true,
		},
		{
			name:         "empty distribution",
			feature:      feature,
			splitValue:   30.0,
			distribution: map[interface{}]int{},
			shouldPanic:  true,
		},
		{
			name:         "negative count in distribution",
			feature:      feature,
			splitValue:   30.0,
			distribution: map[interface{}]int{"yes": -1},
			shouldPanic:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.shouldPanic {
				defer func() {
					if r := recover(); r == nil {
						t.Error("expected panic but none occurred")
					}
				}()
			}

			node := NewDecisionNode(tt.feature, tt.splitValue, tt.distribution)

			if !tt.shouldPanic {
				if node.Feature != tt.feature {
					t.Errorf("feature mismatch")
				}
				if node.SplitValue != tt.splitValue {
					t.Errorf("split value mismatch")
				}
				if node.GetMajorityClass() != "yes" {
					t.Errorf("expected majority class 'yes', got %v", node.GetMajorityClass())
				}
			}
		})
	}
}

func TestDecisionNode_Interface(t *testing.T) {
	feature := NewFeature("age", features.IntegerFeature)
	distribution := map[interface{}]int{"yes": 6, "no": 4}
	node := NewDecisionNode(feature, 30.0, distribution)

	// Test Node interface methods
	if node.IsLeaf() {
		t.Error("decision node should return false for IsLeaf()")
	}
	if node.GetSamples() != 10 {
		t.Errorf("expected 10 samples, got %d", node.GetSamples())
	}
	if node.GetMajorityClass() != "yes" {
		t.Errorf("expected majority class 'yes', got %v", node.GetMajorityClass())
	}
	if node.GetConfidence() != 0.6 {
		t.Errorf("expected confidence 0.6, got %.2f", node.GetConfidence())
	}
	if node.GetNodeType() != "decision" {
		t.Errorf("expected node type 'decision', got %s", node.GetNodeType())
	}
}

func TestDecisionNode_ChildManagement(t *testing.T) {
	feature := NewFeature("age", features.IntegerFeature)
	distribution := map[interface{}]int{"yes": 10}
	parent := NewDecisionNode(feature, 30.0, distribution)

	// Create child nodes
	leftDist := map[interface{}]int{"yes": 8, "no": 2}
	rightDist := map[interface{}]int{"yes": 2, "no": 8}
	leftChild := NewLeafNode(leftDist)
	rightChild := NewLeafNode(rightDist)

	// Test SetChild and GetChild
	parent.SetChild("custom", leftChild)

	retrieved := parent.GetChild("custom")
	if retrieved != leftChild {
		t.Error("retrieved child doesn't match set child")
	}

	// Test convenience methods for binary splits
	parent.SetLeftChild(leftChild)
	parent.SetRightChild(rightChild)

	if parent.GetLeftChild() != leftChild {
		t.Error("left child mismatch")
	}
	if parent.GetRightChild() != rightChild {
		t.Error("right child mismatch")
	}

	// Test GetAllChildren
	allChildren := parent.GetAllChildren()
	if len(allChildren) != 3 { // custom, left, right
		t.Errorf("expected 3 children, got %d", len(allChildren))
	}
}

func TestDecisionNode_Validate(t *testing.T) {
	feature := NewFeature("age", features.IntegerFeature)
	distribution := map[interface{}]int{"yes": 5}

	tests := []struct {
		name        string
		setupNode   func() *DecisionNode
		expectValid bool
	}{
		{
			name: "valid node with children",
			setupNode: func() *DecisionNode {
				node := NewDecisionNode(feature, 30.0, distribution)
				leftChild := NewLeafNode(map[interface{}]int{"yes": 3})
				node.SetLeftChild(leftChild)
				return node
			},
			expectValid: true,
		},
		{
			name: "node without children",
			setupNode: func() *DecisionNode {
				node := NewDecisionNode(feature, 30.0, distribution)
				return node
			},
			expectValid: false,
		},
		{
			name: "node with invalid child",
			setupNode: func() *DecisionNode {
				node := NewDecisionNode(feature, 30.0, distribution)
				// Create invalid child directly
				invalidChild := &LeafNode{
					Prediction: nil, // Invalid
					Samples:    5,
					Confidence: 1.0,
				}
				node.Children["invalid"] = invalidChild
				return node
			},
			expectValid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			node := tt.setupNode()
			isValid := node.Validate()

			if tt.expectValid && !isValid {
				t.Error("expected validation to pass but it failed")
			}
			if !tt.expectValid && isValid {
				t.Error("expected validation to fail but it passed")
			}
		})
	}
}

// ====================
// Serialization Tests
// ====================

func TestLeafNode_Serialization(t *testing.T) {
	distribution := map[interface{}]int{"approved": 7, "rejected": 3}
	leaf := NewLeafNode(distribution)

	// Test ToSerializable
	serializable := leaf.ToSerializable()
	if serializable.Type != "leaf" {
		t.Errorf("expected type 'leaf', got %s", serializable.Type)
	}
	if serializable.Prediction != "approved" {
		t.Errorf("expected prediction 'approved', got %v", serializable.Prediction)
	}
	if serializable.Samples != 10 {
		t.Errorf("expected 10 samples, got %d", serializable.Samples)
	}

	// Check string distribution conversion
	if serializable.ClassDistribution["approved"] != 7 {
		t.Errorf("expected 'approved': 7, got %d", serializable.ClassDistribution["approved"])
	}
	if serializable.ClassDistribution["rejected"] != 3 {
		t.Errorf("expected 'rejected': 3, got %d", serializable.ClassDistribution["rejected"])
	}
}

func TestDecisionNode_Serialization(t *testing.T) {
	feature := NewFeature("age", features.IntegerFeature)
	distribution := map[interface{}]int{"yes": 6, "no": 4}
	decision := NewDecisionNode(feature, 30.0, distribution)

	// Add children
	leftChild := NewLeafNode(map[interface{}]int{"yes": 5, "no": 1})
	rightChild := NewLeafNode(map[interface{}]int{"yes": 1, "no": 3})
	decision.SetLeftChild(leftChild)
	decision.SetRightChild(rightChild)

	// Test ToSerializable
	serializable := decision.ToSerializable()
	if serializable.Type != "decision" {
		t.Errorf("expected type 'decision', got %s", serializable.Type)
	}
	if serializable.Feature.Name != "age" {
		t.Errorf("expected feature name 'age', got %s", serializable.Feature.Name)
	}
	if serializable.SplitValue != 30.0 {
		t.Errorf("expected split value 30.0, got %v", serializable.SplitValue)
	}
	if len(serializable.Children) != 2 {
		t.Errorf("expected 2 children, got %d", len(serializable.Children))
	}

	// Check children serialization
	if serializable.Children["left"] == nil {
		t.Error("left child not serialized")
	}
	if serializable.Children["right"] == nil {
		t.Error("right child not serialized")
	}
}

func TestSerialization_RoundTrip(t *testing.T) {
	// Create a simple tree structure
	feature := NewFeature("age", features.IntegerFeature)
	distribution := map[interface{}]int{"approved": 10, "rejected": 8}
	root := NewDecisionNode(feature, 30.0, distribution)

	leftDist := map[interface{}]int{"approved": 8, "rejected": 2}
	rightDist := map[interface{}]int{"approved": 2, "rejected": 6}
	leftChild := NewLeafNode(leftDist)
	rightChild := NewLeafNode(rightDist)

	root.SetLeftChild(leftChild)
	root.SetRightChild(rightChild)

	// Serialize to JSON
	serializable := root.ToSerializable()
	jsonData, err := json.MarshalIndent(serializable, "", "  ")
	if err != nil {
		t.Fatalf("failed to marshal JSON: %v", err)
	}

	// Deserialize from JSON
	var deserializedSerializable SerializableNode
	err = json.Unmarshal(jsonData, &deserializedSerializable)
	if err != nil {
		t.Fatalf("failed to unmarshal JSON: %v", err)
	}

	// Convert back to Node
	reconstructed := FromSerializable(&deserializedSerializable)

	// Validate reconstruction
	if reconstructed.IsLeaf() {
		t.Error("root should be a decision node")
	}

	decisionNode := reconstructed.(*DecisionNode)
	if decisionNode.Feature.Name != "age" {
		t.Errorf("expected feature 'age', got %s", decisionNode.Feature.Name)
	}
	if decisionNode.SplitValue != 30.0 {
		t.Errorf("expected split value 30.0, got %v", decisionNode.SplitValue)
	}

	// Check children
	leftReconstructed := decisionNode.GetLeftChild()
	rightReconstructed := decisionNode.GetRightChild()

	if leftReconstructed == nil || rightReconstructed == nil {
		t.Error("children not properly reconstructed")
	}

	if !leftReconstructed.IsLeaf() || !rightReconstructed.IsLeaf() {
		t.Error("children should be leaf nodes")
	}

	// Validate statistics preservation
	if leftReconstructed.GetMajorityClass() != "approved" {
		t.Errorf("left child majority class mismatch")
	}
	if rightReconstructed.GetMajorityClass() != "rejected" {
		t.Errorf("right child majority class mismatch")
	}
}

// ====================
// Tree Utility Function Tests
// ====================

func TestCountNodes(t *testing.T) {
	tests := []struct {
		name          string
		setupTree     func() Node
		expectedCount int
	}{
		{
			name: "nil node",
			setupTree: func() Node {
				return nil
			},
			expectedCount: 0,
		},
		{
			name: "single leaf node",
			setupTree: func() Node {
				leaf := NewLeafNode(map[interface{}]int{"yes": 5})
				return leaf
			},
			expectedCount: 1,
		},
		{
			name: "decision node with two leaf children",
			setupTree: func() Node {
				feature := NewFeature("age", features.IntegerFeature)
				root := NewDecisionNode(feature, 30.0, map[interface{}]int{"yes": 10})

				leftChild := NewLeafNode(map[interface{}]int{"yes": 6})
				rightChild := NewLeafNode(map[interface{}]int{"no": 4})

				root.SetLeftChild(leftChild)
				root.SetRightChild(rightChild)

				return root
			},
			expectedCount: 3, // root + 2 leaves
		},
		{
			name: "deeper tree",
			setupTree: func() Node {
				// Root: age <= 30
				ageFeature := NewFeature("age", features.IntegerFeature)
				root := NewDecisionNode(ageFeature, 30.0, map[interface{}]int{"yes": 20})

				// Left subtree: another decision node
				salaryFeature := NewFeature("salary", features.FloatFeature)
				leftDecision := NewDecisionNode(salaryFeature, 50000.0, map[interface{}]int{"yes": 12})
				leftLeft := NewLeafNode(map[interface{}]int{"yes": 8})
				leftRight := NewLeafNode(map[interface{}]int{"no": 4})
				leftDecision.SetLeftChild(leftLeft)
				leftDecision.SetRightChild(leftRight)

				// Right subtree: leaf node
				rightLeaf := NewLeafNode(map[interface{}]int{"no": 8})

				root.SetLeftChild(leftDecision)
				root.SetRightChild(rightLeaf)

				return root
			},
			expectedCount: 5, // root + leftDecision + leftLeft + leftRight + rightLeaf
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tree := tt.setupTree()
			count := CountNodes(tree)
			if count != tt.expectedCount {
				t.Errorf("expected %d nodes, got %d", tt.expectedCount, count)
			}
		})
	}
}

func TestCountLeaves(t *testing.T) {
	tests := []struct {
		name          string
		setupTree     func() Node
		expectedCount int
	}{
		{
			name: "nil node",
			setupTree: func() Node {
				return nil
			},
			expectedCount: 0,
		},
		{
			name: "single leaf node",
			setupTree: func() Node {
				leaf := NewLeafNode(map[interface{}]int{"yes": 5})
				return leaf
			},
			expectedCount: 1,
		},
		{
			name: "decision node with two leaf children",
			setupTree: func() Node {
				feature := NewFeature("age", features.IntegerFeature)
				root := NewDecisionNode(feature, 30.0, map[interface{}]int{"yes": 10})

				leftChild := NewLeafNode(map[interface{}]int{"yes": 6})
				rightChild := NewLeafNode(map[interface{}]int{"no": 4})

				root.SetLeftChild(leftChild)
				root.SetRightChild(rightChild)

				return root
			},
			expectedCount: 2, // 2 leaf nodes
		},
		{
			name: "deeper tree with mixed nodes",
			setupTree: func() Node {
				// Same tree as in TestCountNodes
				ageFeature := NewFeature("age", features.IntegerFeature)
				root := NewDecisionNode(ageFeature, 30.0, map[interface{}]int{"yes": 20})

				salaryFeature := NewFeature("salary", features.FloatFeature)
				leftDecision := NewDecisionNode(salaryFeature, 50000.0, map[interface{}]int{"yes": 12})
				leftLeft := NewLeafNode(map[interface{}]int{"yes": 8})
				leftRight := NewLeafNode(map[interface{}]int{"no": 4})
				leftDecision.SetLeftChild(leftLeft)
				leftDecision.SetRightChild(leftRight)

				rightLeaf := NewLeafNode(map[interface{}]int{"no": 8})

				root.SetLeftChild(leftDecision)
				root.SetRightChild(rightLeaf)

				return root
			},
			expectedCount: 3, // leftLeft + leftRight + rightLeaf
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tree := tt.setupTree()
			count := CountLeaves(tree)
			if count != tt.expectedCount {
				t.Errorf("expected %d leaves, got %d", tt.expectedCount, count)
			}
		})
	}
}

// ====================
// FromSerializable Tests
// ====================

func TestFromSerializable(t *testing.T) {
	tests := []struct {
		name        string
		setupSerial func() *SerializableNode
		shouldPanic bool
		expectType  string
	}{
		{
			name: "valid leaf node",
			setupSerial: func() *SerializableNode {
				return &SerializableNode{
					Type:              "leaf",
					Prediction:        "approved",
					ClassDistribution: map[string]int{"approved": 8, "rejected": 2},
					Samples:           10,
					Confidence:        0.8,
				}
			},
			shouldPanic: false,
			expectType:  "leaf",
		},
		{
			name: "valid decision node",
			setupSerial: func() *SerializableNode {
				feature := &Feature{Name: "age", Type: features.IntegerFeature}
				return &SerializableNode{
					Type:       "decision",
					Feature:    feature,
					SplitValue: 30.0,
					Children: map[string]*SerializableNode{
						"left": {
							Type:              "leaf",
							Prediction:        "approved",
							ClassDistribution: map[string]int{"approved": 5},
							Samples:           5,
							Confidence:        1.0,
						},
					},
					ClassDistribution: map[string]int{"approved": 5, "rejected": 3},
					Samples:           8,
					Confidence:        0.625,
				}
			},
			shouldPanic: false,
			expectType:  "decision",
		},
		{
			name: "nil serializable node",
			setupSerial: func() *SerializableNode {
				return nil
			},
			shouldPanic: true,
		},
		{
			name: "unknown node type",
			setupSerial: func() *SerializableNode {
				return &SerializableNode{
					Type: "unknown",
				}
			},
			shouldPanic: true,
		},
		{
			name: "leaf without prediction",
			setupSerial: func() *SerializableNode {
				return &SerializableNode{
					Type:       "leaf",
					Prediction: nil,
					Samples:    5,
					Confidence: 1.0,
				}
			},
			shouldPanic: true,
		},
		{
			name: "decision without feature",
			setupSerial: func() *SerializableNode {
				return &SerializableNode{
					Type:       "decision",
					Feature:    nil,
					SplitValue: 30.0,
				}
			},
			shouldPanic: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.shouldPanic {
				defer func() {
					if r := recover(); r == nil {
						t.Error("expected panic but none occurred")
					}
				}()
			}

			serializable := tt.setupSerial()
			node := FromSerializable(serializable)

			if !tt.shouldPanic {
				if node.GetNodeType() != tt.expectType {
					t.Errorf("expected node type %s, got %s", tt.expectType, node.GetNodeType())
				}

				// Validate the reconstructed node
				if !node.Validate() {
					t.Error("reconstructed node validation failed")
				}
			}
		})
	}
}

// ====================
// Integration Tests
// ====================

func TestCompleteTreeExample(t *testing.T) {
	// Create a complete small tree to test all components together

	// Root: age <= 30
	ageFeature := NewFeature("age", features.IntegerFeature)
	root := NewDecisionNode(ageFeature, 30.0, map[interface{}]int{"approved": 10, "rejected": 8})

	// Left child: approved (young people mostly approved)
	leftChild := NewLeafNode(map[interface{}]int{"approved": 8, "rejected": 2})

	// Right child: department split
	deptFeature := NewFeature("department", features.StringFeature)
	rightDecision := NewDecisionNode(deptFeature, "engineering", map[interface{}]int{"approved": 2, "rejected": 6})

	// Right subtree leaves
	engLeaf := NewLeafNode(map[interface{}]int{"approved": 2, "rejected": 1})
	nonEngLeaf := NewLeafNode(map[interface{}]int{"rejected": 5})

	rightDecision.SetLeftChild(engLeaf)     // department == engineering
	rightDecision.SetRightChild(nonEngLeaf) // department != engineering

	root.SetLeftChild(leftChild)
	root.SetRightChild(rightDecision)

	// Test tree structure
	totalNodes := CountNodes(root)
	totalLeaves := CountLeaves(root)

	if totalNodes != 5 {
		t.Errorf("expected 5 total nodes, got %d", totalNodes)
	}
	if totalLeaves != 3 {
		t.Errorf("expected 3 leaf nodes, got %d", totalLeaves)
	}

	// Test serialization round trip
	serializable := root.ToSerializable()
	jsonData, err := json.MarshalIndent(serializable, "", "  ")
	if err != nil {
		t.Fatalf("JSON serialization failed: %v", err)
	}

	var deserializedSerializable SerializableNode
	err = json.Unmarshal(jsonData, &deserializedSerializable)
	if err != nil {
		t.Fatalf("JSON deserialization failed: %v", err)
	}

	reconstructed := FromSerializable(&deserializedSerializable)

	// Validate reconstructed tree structure
	if reconstructed.IsLeaf() {
		t.Error("reconstructed root should be decision node")
	}

	reconstructedRoot := reconstructed.(*DecisionNode)
	if reconstructedRoot.Feature.Name != "age" {
		t.Errorf("reconstructed root feature name mismatch")
	}

	// Validate tree statistics preservation
	if CountNodes(reconstructed) != totalNodes {
		t.Error("node count changed after serialization")
	}
	if CountLeaves(reconstructed) != totalLeaves {
		t.Error("leaf count changed after serialization")
	}

	// Validate child structure preservation
	leftReconstructed := reconstructedRoot.GetLeftChild()
	rightReconstructed := reconstructedRoot.GetRightChild()

	if leftReconstructed == nil || !leftReconstructed.IsLeaf() {
		t.Error("left child structure not preserved")
	}
	if rightReconstructed == nil || rightReconstructed.IsLeaf() {
		t.Error("right child should be decision node")
	}

	// Validate deeper structure (right subtree)
	rightDecisionReconstructed := rightReconstructed.(*DecisionNode)
	if rightDecisionReconstructed.Feature.Name != "department" {
		t.Error("right subtree feature not preserved")
	}

	if rightDecisionReconstructed.GetLeftChild() == nil || rightDecisionReconstructed.GetRightChild() == nil {
		t.Error("right subtree children not preserved")
	}
}

// ====================
// Edge Case Tests
// ====================

func TestEdgeCases(t *testing.T) {
	t.Run("single sample leaf", func(t *testing.T) {
		leaf := NewLeafNode(map[interface{}]int{"single": 1})
		if leaf.GetConfidence() != 1.0 {
			t.Errorf("single sample should have 100%% confidence, got %.2f", leaf.GetConfidence())
		}
	})

	t.Run("tied classes in distribution", func(t *testing.T) {
		// When multiple classes have same count, any can be majority
		dist := map[interface{}]int{"A": 5, "B": 5}
		leaf := NewLeafNode(dist)

		majority := leaf.GetMajorityClass()
		if majority != "A" && majority != "B" {
			t.Errorf("majority class should be A or B, got %v", majority)
		}
		if leaf.GetConfidence() != 0.5 {
			t.Errorf("tied distribution should have 0.5 confidence, got %.2f", leaf.GetConfidence())
		}
	})

	t.Run("decision node without required children", func(t *testing.T) {
		feature := NewFeature("test", features.IntegerFeature)
		node := NewDecisionNode(feature, 5.0, map[interface{}]int{"yes": 10})

		// Should fail validation without children
		if node.Validate() {
			t.Error("decision node without children should fail validation")
		}
	})

	t.Run("numeric vs categorical feature types", func(t *testing.T) {
		intFeature := NewFeature("age", features.IntegerFeature)
		floatFeature := NewFeature("salary", features.FloatFeature)
		stringFeature := NewFeature("dept", features.StringFeature)

		if !intFeature.IsNumerical() || intFeature.IsCategorical() {
			t.Error("integer feature type classification failed")
		}
		if !floatFeature.IsNumerical() || floatFeature.IsCategorical() {
			t.Error("float feature type classification failed")
		}
		if stringFeature.IsNumerical() || !stringFeature.IsCategorical() {
			t.Error("string feature type classification failed")
		}
	})

	t.Run("large class distribution", func(t *testing.T) {
		// Test with many classes
		largeDist := make(map[interface{}]int)
		for i := 0; i < 100; i++ {
			largeDist[fmt.Sprintf("class_%d", i)] = i + 1 // class_99 has count 100 (majority)
		}

		leaf := NewLeafNode(largeDist)

		if leaf.GetMajorityClass() != "class_99" {
			t.Errorf("expected majority class_99, got %v", leaf.GetMajorityClass())
		}

		expectedSamples := (100 * 101) / 2 // sum of 1+2+...+100
		if leaf.GetSamples() != expectedSamples {
			t.Errorf("expected %d samples, got %d", expectedSamples, leaf.GetSamples())
		}
	})
}

// ====================
// Performance Tests
// ====================

func BenchmarkLeafNodeCreation(b *testing.B) {
	distribution := map[interface{}]int{
		"class_A": 100,
		"class_B": 50,
		"class_C": 25,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		NewLeafNode(distribution)
	}
}

func BenchmarkDecisionNodeCreation(b *testing.B) {
	feature := NewFeature("test_feature", features.IntegerFeature)
	distribution := map[interface{}]int{
		"class_A": 100,
		"class_B": 50,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		NewDecisionNode(feature, 30.0, distribution)
	}
}

func BenchmarkSerialization(b *testing.B) {
	// Create a moderately complex tree
	feature := NewFeature("age", features.IntegerFeature)
	root := NewDecisionNode(feature, 30.0, map[interface{}]int{"yes": 100, "no": 50})

	leftChild := NewLeafNode(map[interface{}]int{"yes": 80, "no": 20})
	rightChild := NewLeafNode(map[interface{}]int{"no": 30, "yes": 20})

	root.SetLeftChild(leftChild)
	root.SetRightChild(rightChild)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		serializable := root.ToSerializable()
		_, err := json.Marshal(serializable)
		if err != nil {
			b.Fatalf("serialization failed: %v", err)
		}
	}
}

func BenchmarkDeserialization(b *testing.B) {
	// Create serialized data
	feature := &Feature{Name: "age", Type: features.IntegerFeature}
	serializable := &SerializableNode{
		Type:       "decision",
		Feature:    feature,
		SplitValue: 30.0,
		Children: map[string]*SerializableNode{
			"left": {
				Type:              "leaf",
				Prediction:        "approved",
				ClassDistribution: map[string]int{"approved": 80, "rejected": 20},
				Samples:           100,
				Confidence:        0.8,
			},
			"right": {
				Type:              "leaf",
				Prediction:        "rejected",
				ClassDistribution: map[string]int{"rejected": 30, "approved": 20},
				Samples:           50,
				Confidence:        0.6,
			},
		},
		ClassDistribution: map[string]int{"approved": 100, "rejected": 50},
		Samples:           150,
		Confidence:        0.667,
	}

	jsonData, _ := json.Marshal(serializable)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var deserializedSerializable SerializableNode
		err := json.Unmarshal(jsonData, &deserializedSerializable)
		if err != nil {
			b.Fatalf("JSON unmarshal failed: %v", err)
		}

		FromSerializable(&deserializedSerializable)
	}
}
